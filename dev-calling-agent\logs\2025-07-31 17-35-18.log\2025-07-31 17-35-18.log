[ 2025-07-31 17:35:22,686 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-31 17:35:23,137 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4514 seconds.
[ 2025-07-31 17:35:28,237 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-31 17:35:38,048 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:35:38,049 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:35:38,051 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-31 17:37:59,903 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:37:59,903 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:37:59,906 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-31 17:37:59,906 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-31 17:37:59,914 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-31 17:37:59,983 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,983 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,984 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,984 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,986 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,987 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,988 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,988 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,989 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,991 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,993 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:37:59,995 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:38:00,033 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,034 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,034 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,034 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,036 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,036 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,036 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,036 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,037 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,037 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,037 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,037 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:00,614 ] 784 livekit.agents - INFO - registered worker
[ 2025-07-31 17:38:41,554 ] 855 livekit.agents - INFO - received job request
[ 2025-07-31 17:38:41,697 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:38:41,698 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:38:42,964 ] 527 root - INFO - Call logging started for call ID: SCL_uZpTMMsrbPGK
[ 2025-07-31 17:38:43,541 ] 582 root - ERROR - Voice agent error: AgentSession.generate_reply() takes 1 positional argument but 2 were given
[ 2025-07-31 17:38:43,542 ] 589 root - INFO - Call logging ended for call ID: SCL_uZpTMMsrbPGK
[ 2025-07-31 17:38:43,543 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 574, in entrypoint
    await session.generate_reply(
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 486, in generate_reply
    reply = await super().generate_reply(instructions, **kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: AgentSession.generate_reply() takes 1 positional argument but 2 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 583, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\twillio-integrated\dev-calling-agent\langgraph-agent.py] line number [574] error message [AgentSession.generate_reply() takes 1 positional argument but 2 were given]
[ 2025-07-31 17:39:52,308 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-07-31 17:40:12,846 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-07-31 17:40:21,226 ] 477 livekit.agents - INFO - draining worker
[ 2025-07-31 17:40:21,228 ] 560 livekit.agents - INFO - shutting down worker
