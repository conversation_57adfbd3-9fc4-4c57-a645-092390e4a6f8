#!/usr/bin/env python3
"""
Test script to verify LiveKit transcription integration with call logging.
This script simulates the new text stream transcription functionality.
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import Mock, MagicMock

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.logging.call_logger import call_logger
from livekit import rtc

class MockParticipant:
    """Mock participant for testing"""
    def __init__(self, identity: str, kind: rtc.ParticipantKind):
        self.identity = identity
        self.kind = kind
        self.attributes = {
            'sip.phoneNumber': '+1234567890',
            'sip.trunkPhoneNumber': '+0987654321',
            'sip.trunkID': 'test-trunk-001',
            'sip.callStatus': 'in-progress',
            'sip.callID': f'test-call-{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        }

class MockTextStreamReader:
    """Mock text stream reader for testing"""
    def __init__(self, message: str, attributes: dict = None):
        self.message = message
        self.info = Mock()
        self.info.attributes = attributes or {}
    
    async def readAll(self):
        return self.message

class MockParticipantInfo:
    """Mock participant info for testing"""
    def __init__(self, identity: str):
        self.identity = identity

async def test_transcription_stream_logging():
    """Test the transcription stream logging functionality"""
    print("🧪 Testing LiveKit transcription stream integration...")
    
    # Create mock participant (SIP user)
    sip_participant = MockParticipant("user_12345", rtc.ParticipantKind.PARTICIPANT_KIND_SIP)
    
    # Start a test call
    call_id = call_logger.start_call(sip_participant)
    if not call_id:
        print("❌ Failed to start test call")
        return False
    
    print(f"✅ Test call started with ID: {call_id}")
    
    try:
        # Import the transcription handler
        import importlib.util
        spec = importlib.util.spec_from_file_location("langgraph_agent", "langgraph-agent.py")
        langgraph_agent = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(langgraph_agent)

        # Set the global call ID for testing
        langgraph_agent.current_call_id = call_id
        
        # Test 1: User transcription with track ID (real transcription)
        print("\n📝 Testing user transcription stream...")
        user_reader = MockTextStreamReader(
            "Hello, I need help with my account",
            attributes={'lk.transcribed_track_id': 'track_12345'}
        )
        user_participant_info = MockParticipantInfo("user_12345")
        
        await langgraph_agent.handle_transcription_stream(user_reader, user_participant_info)
        
        # Test 2: Agent transcription with track ID
        print("📝 Testing agent transcription stream...")
        agent_reader = MockTextStreamReader(
            "I'd be happy to help you with your account. What specific issue are you experiencing?",
            attributes={'lk.transcribed_track_id': 'track_67890'}
        )
        agent_participant_info = MockParticipantInfo("agent_voice_assistant")
        
        await langgraph_agent.handle_transcription_stream(agent_reader, agent_participant_info)

        # Test 3: Chat message (no track ID)
        print("📝 Testing chat message stream...")
        chat_reader = MockTextStreamReader(
            "I can't access my online banking",
            attributes={}  # No track ID means it's a chat message
        )

        await langgraph_agent.handle_transcription_stream(chat_reader, user_participant_info)

        # Test 4: Another agent response
        print("📝 Testing another agent transcription...")
        agent_reader2 = MockTextStreamReader(
            "Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?",
            attributes={'lk.transcribed_track_id': 'track_67891'}
        )

        await langgraph_agent.handle_transcription_stream(agent_reader2, agent_participant_info)
        
        # Set language detection
        call_logger.set_language(call_id, "English")
        
        print("\n✅ All transcription stream tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during transcription testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # End the test call
        call_logger.end_call(call_id)
        print(f"📞 Test call ended: {call_id}")

def check_log_files():
    """Check if log files were created properly"""
    print("\n📁 Checking log files...")
    
    log_dir = "call_logs"
    if not os.path.exists(log_dir):
        print(f"❌ Log directory {log_dir} does not exist")
        return False
    
    # List recent log files
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.txt')]
    if log_files:
        print(f"✅ Found {len(log_files)} log files:")
        for log_file in sorted(log_files)[-5:]:  # Show last 5 files
            print(f"  📄 {log_file}")
        
        # Show content of the most recent call log
        call_logs = [f for f in log_files if f.startswith('call_') and not f.startswith('call_events_')]
        if call_logs:
            latest_call_log = sorted(call_logs)[-1]
            print(f"\n📖 Content of latest call log ({latest_call_log}):")
            try:
                with open(os.path.join(log_dir, latest_call_log), 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("=" * 60)
                    print(content)
                    print("=" * 60)
            except Exception as e:
                print(f"❌ Error reading log file: {e}")
    else:
        print("❌ No log files found")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 Starting LiveKit Transcription Stream Integration Tests")
    print("=" * 70)
    
    success = True
    
    # Test 1: Transcription stream logging
    if not await test_transcription_stream_logging():
        success = False
    
    # Test 2: Check log files
    if not check_log_files():
        success = False
    
    print("\n" + "=" * 70)
    if success:
        print("✅ All tests passed! LiveKit transcription stream integration is working correctly.")
        print("\n📋 Next steps:")
        print("1. Run your voice agent: python langgraph-agent.py start")
        print("2. Make a test call to your Twilio number")
        print("3. Check the call_logs/ directory for real-time transcriptions")
        print("4. Verify transcriptions appear with [LIVE_TRANSCRIPTION] tags")
        print("\n🔍 What to look for in logs:")
        print("- [LiveKit_TextStream] [LIVE_TRANSCRIPTION] tags for real-time transcriptions")
        print("- [LiveKit_Chat] [CHAT] tags for chat messages")
        print("- Track IDs for transcription correlation")
        print("- Participant identities for speaker identification")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
