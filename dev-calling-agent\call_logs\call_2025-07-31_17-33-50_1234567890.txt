================================================================================
CALL LOG SUMMARY
================================================================================

Call ID: test-call-20250731_173350
Caller Phone Number: +**********
Trunk Phone Number: +**********
Trunk ID: test-trunk-001
Call Status: in-progress
Start Time: 2025-07-31T17:33:50.958738
End Time: 2025-07-31T17:33:50.966439
Duration: 0.01 seconds
Language Detected: English (en)

================================================================================
CONVERSATION TRANSCRIPT
================================================================================

[2025-07-31T17:33:50.959925] 👤 USER [STT]:
Hello, I need help with my account

[2025-07-31T17:33:50.960411] 👤 USER [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: user_stt...]:
Hello, I need help with my account

[2025-07-31T17:33:50.960971] 🤖 AGENT [RESPONSE]:
I'd be happy to help you with your account. What specific issue are you experiencing?

[2025-07-31T17:33:50.961375] 🤖 AGENT [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: agent_re...]:
I'd be happy to help you with your account. What specific issue are you experiencing?

[2025-07-31T17:33:50.961908] 👤 USER [STT]:
I can't access my online banking

[2025-07-31T17:33:50.962297] 👤 USER [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: user_tur...]:
I can't access my online banking

[2025-07-31T17:33:50.962819] 🤖 AGENT [RESPONSE]:
I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.

[2025-07-31T17:33:50.963145] 🤖 AGENT [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: agent_tu...]:
I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.

[2025-07-31T17:33:50.963544] 👤 USER [STT]:
The error says 'Invalid credentials'

[2025-07-31T17:33:50.963866] 👤 USER [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: user_tur...]:
The error says 'Invalid credentials'

[2025-07-31T17:33:50.964244] 🤖 AGENT [RESPONSE]:
I see the issue. Let me help you reset your password securely.

[2025-07-31T17:33:50.964747] 🤖 AGENT [LiveKit_TextStream] [LIVE_TRANSCRIPTION] [Track: agent_tu...]:
I see the issue. Let me help you reset your password securely.

================================================================================
RAW JSON DATA
================================================================================
{
  "call_id": "test-call-20250731_173350",
  "caller_phone_number": "+**********",
  "trunk_phone_number": "+**********",
  "trunk_id": "test-trunk-001",
  "call_status": "in-progress",
  "start_time": "2025-07-31T17:33:50.958738",
  "end_time": "2025-07-31T17:33:50.966439",
  "duration_seconds": 0.007701,
  "twilio_call_sid": null,
  "twilio_account_sid": null,
  "language_detected": "English (en)",
  "transcriptions": [
    {
      "timestamp": "2025-07-31T17:33:50.959925",
      "speaker": "USER",
      "text": "Hello, I need help with my account",
      "type": "transcription"
    },
    {
      "timestamp": "2025-07-31T17:33:50.960411",
      "speaker": "USER",
      "text": "Hello, I need help with my account",
      "type": "livekit_transcription",
      "track_id": "user_stt",
      "participant_identity": "user_participant",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:33:50.961908",
      "speaker": "USER",
      "text": "I can't access my online banking",
      "type": "transcription"
    },
    {
      "timestamp": "2025-07-31T17:33:50.962297",
      "speaker": "USER",
      "text": "I can't access my online banking",
      "type": "livekit_transcription",
      "track_id": "user_turn_1",
      "participant_identity": "user_participant",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:33:50.963544",
      "speaker": "USER",
      "text": "The error says 'Invalid credentials'",
      "type": "transcription"
    },
    {
      "timestamp": "2025-07-31T17:33:50.963866",
      "speaker": "USER",
      "text": "The error says 'Invalid credentials'",
      "type": "livekit_transcription",
      "track_id": "user_turn_3",
      "participant_identity": "user_participant",
      "source": "LiveKit_TextStream"
    }
  ],
  "agent_responses": [
    {
      "timestamp": "2025-07-31T17:33:50.960971",
      "speaker": "AGENT",
      "text": "I'd be happy to help you with your account. What specific issue are you experiencing?",
      "type": "response"
    },
    {
      "timestamp": "2025-07-31T17:33:50.961375",
      "speaker": "AGENT",
      "text": "I'd be happy to help you with your account. What specific issue are you experiencing?",
      "type": "livekit_transcription",
      "track_id": "agent_response",
      "participant_identity": "agent_session",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:33:50.962819",
      "speaker": "AGENT",
      "text": "I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.",
      "type": "response"
    },
    {
      "timestamp": "2025-07-31T17:33:50.963145",
      "speaker": "AGENT",
      "text": "I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.",
      "type": "livekit_transcription",
      "track_id": "agent_turn_2",
      "participant_identity": "agent_participant",
      "source": "LiveKit_TextStream"
    },
    {
      "timestamp": "2025-07-31T17:33:50.964244",
      "speaker": "AGENT",
      "text": "I see the issue. Let me help you reset your password securely.",
      "type": "response"
    },
    {
      "timestamp": "2025-07-31T17:33:50.964747",
      "speaker": "AGENT",
      "text": "I see the issue. Let me help you reset your password securely.",
      "type": "livekit_transcription",
      "track_id": "agent_turn_4",
      "participant_identity": "agent_participant",
      "source": "LiveKit_TextStream"
    }
  ]
}