import os
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import threading
from pathlib import Path

try:
    from livekit import rtc
except ImportError:
    # Fallback for testing without LiveKit
    class rtc:
        class ParticipantKind:
            PARTICIPANT_KIND_SIP = "sip"

@dataclass
class CallInfo:
    """Data class to store call information"""
    call_id: str
    caller_phone_number: str
    trunk_phone_number: str
    trunk_id: str
    call_status: str
    start_time: str
    end_time: Optional[str] = None
    duration_seconds: Optional[float] = None
    twilio_call_sid: Optional[str] = None
    twilio_account_sid: Optional[str] = None
    language_detected: Optional[str] = None
    transcriptions: List[Dict[str, Any]] = None
    agent_responses: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.transcriptions is None:
            self.transcriptions = []
        if self.agent_responses is None:
            self.agent_responses = []

class CallLogger:
    """Enhanced call logging system for SIP/Twilio calls"""
    
    def __init__(self, log_directory: str = "call_logs"):
        self.log_directory = Path(log_directory)
        self.log_directory.mkdir(exist_ok=True)
        self.active_calls: Dict[str, CallInfo] = {}
        self.lock = threading.Lock()
        
    def start_call(self, participant) -> Optional[str]:
        """Start logging a new call from SIP participant"""
        try:
            # Check if participant is SIP participant
            if not hasattr(participant, 'kind') or participant.kind != rtc.ParticipantKind.PARTICIPANT_KIND_SIP:
                return None
                
            # Extract SIP attributes
            attributes = participant.attributes
            call_id = attributes.get('sip.callID', f"unknown_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            call_info = CallInfo(
                call_id=call_id,
                caller_phone_number=attributes.get('sip.phoneNumber', 'Unknown'),
                trunk_phone_number=attributes.get('sip.trunkPhoneNumber', 'Unknown'),
                trunk_id=attributes.get('sip.trunkID', 'Unknown'),
                call_status=attributes.get('sip.callStatus', 'Unknown'),
                start_time=datetime.now().isoformat(),
                twilio_call_sid=attributes.get('sip.twilio.callSid'),
                twilio_account_sid=attributes.get('sip.twilio.accountSid')
            )
            
            with self.lock:
                self.active_calls[call_id] = call_info
                
            # Log call start
            self._log_call_event(call_id, "CALL_STARTED", {
                "caller": call_info.caller_phone_number,
                "trunk": call_info.trunk_phone_number,
                "status": call_info.call_status
            })
            
            return call_id
            
        except Exception as e:
            print(f"Error starting call log: {e}")
            return None
    
    def log_user_transcription(self, call_id: str, transcription: str, timestamp: Optional[str] = None):
        """Log user voice transcription"""
        if call_id not in self.active_calls:
            return
            
        timestamp = timestamp or datetime.now().isoformat()
        
        transcription_entry = {
            "timestamp": timestamp,
            "speaker": "USER",
            "text": transcription,
            "type": "transcription"
        }
        
        with self.lock:
            self.active_calls[call_id].transcriptions.append(transcription_entry)
            
            # Update the call log file directly
            log_file = f"call_logs/call_{datetime.fromtimestamp(self.active_calls[call_id].start_time).strftime('%Y-%m-%d_%H-%M-%S')}_{self.active_calls[call_id].caller_phone_number.replace('+', '')}.txt"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n[{timestamp}] 👤 USER:\n{transcription}\n")
            
        self._log_call_event(call_id, "USER_TRANSCRIPTION", transcription_entry)
    
    def log_agent_response(self, call_id: str, response: str, timestamp: Optional[str] = None):
        """Log agent response"""
        if call_id not in self.active_calls:
            return
            
        timestamp = timestamp or datetime.now().isoformat()
        
        response_entry = {
            "timestamp": timestamp,
            "speaker": "AGENT",
            "text": response,
            "type": "response"
        }
        
        with self.lock:
            self.active_calls[call_id].agent_responses.append(response_entry)
            
            # Update the call log file directly
            log_file = f"call_logs/call_{datetime.fromtimestamp(self.active_calls[call_id].start_time).strftime('%Y-%m-%d_%H-%M-%S')}_{self.active_calls[call_id].caller_phone_number.replace('+', '')}.txt"
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n[{timestamp}] 🤖 AGENT:\n{response}\n")
            
        self._log_call_event(call_id, "AGENT_RESPONSE", response_entry)
    
    def log_transcription_stream(self, call_id: str, transcription: str, speaker: str,
                               track_id: Optional[str] = None, participant_identity: Optional[str] = None,
                               timestamp: Optional[str] = None):
        """Log transcription from LiveKit text streams"""
        if call_id not in self.active_calls:
            return

        timestamp = timestamp or datetime.now().isoformat()

        transcription_entry = {
            "timestamp": timestamp,
            "speaker": speaker,
            "text": transcription,
            "type": "livekit_transcription",
            "track_id": track_id,
            "participant_identity": participant_identity,
            "source": "LiveKit_TextStream"
        }

        with self.lock:
            if speaker == "USER":
                self.active_calls[call_id].transcriptions.append(transcription_entry)
            else:
                self.active_calls[call_id].agent_responses.append(transcription_entry)

        self._log_call_event(call_id, f"{speaker}_LIVEKIT_TRANSCRIPTION", transcription_entry)

    def log_chat_message(self, call_id: str, message: str, participant_identity: str,
                        timestamp: Optional[str] = None):
        """Log chat message from LiveKit text streams"""
        if call_id not in self.active_calls:
            return

        timestamp = timestamp or datetime.now().isoformat()

        chat_entry = {
            "timestamp": timestamp,
            "speaker": "USER" if not participant_identity.startswith('agent') else "AGENT",
            "text": message,
            "type": "chat_message",
            "participant_identity": participant_identity,
            "source": "LiveKit_Chat"
        }

        with self.lock:
            # Store chat messages in transcriptions for now (could create separate chat log if needed)
            self.active_calls[call_id].transcriptions.append(chat_entry)

        self._log_call_event(call_id, "CHAT_MESSAGE", chat_entry)

    def set_language(self, call_id: str, language: str):
        """Set detected language for the call"""
        if call_id in self.active_calls:
            with self.lock:
                self.active_calls[call_id].language_detected = language
            self._log_call_event(call_id, "LANGUAGE_DETECTED", {"language": language})
    
    def end_call(self, call_id: str):
        """End call logging and save complete log"""
        if call_id not in self.active_calls:
            return
            
        with self.lock:
            call_info = self.active_calls[call_id]
            call_info.end_time = datetime.now().isoformat()
            
            # Calculate duration
            start_dt = datetime.fromisoformat(call_info.start_time)
            end_dt = datetime.fromisoformat(call_info.end_time)
            call_info.duration_seconds = (end_dt - start_dt).total_seconds()
            
            # Save complete call log
            self._save_complete_call_log(call_info)
            
            # Remove from active calls
            del self.active_calls[call_id]
            
        self._log_call_event(call_id, "CALL_ENDED", {
            "duration": call_info.duration_seconds,
            "end_time": call_info.end_time
        })
    
    def _log_call_event(self, call_id: str, event_type: str, data: Dict[str, Any]):
        """Log individual call events to real-time log"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "call_id": call_id,
            "event_type": event_type,
            "data": data
        }
        
        # Write to real-time log file
        log_file = self.log_directory / f"call_events_{datetime.now().strftime('%Y-%m-%d')}.txt"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"{json.dumps(log_entry, ensure_ascii=False)}\n")
    
    def _save_complete_call_log(self, call_info: CallInfo):
        """Save complete call information to individual file"""
        # Create filename with timestamp and caller number
        safe_caller = call_info.caller_phone_number.replace('+', '').replace('-', '').replace(' ', '')
        filename = f"call_{call_info.start_time[:10]}_{call_info.start_time[11:19].replace(':', '-')}_{safe_caller}.txt"
        
        log_file = self.log_directory / filename
        
        # Prepare complete call data
        call_data = asdict(call_info)
        
        # Create human-readable format
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("CALL LOG SUMMARY\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"Call ID: {call_info.call_id}\n")
            f.write(f"Caller Phone Number: {call_info.caller_phone_number}\n")
            f.write(f"Trunk Phone Number: {call_info.trunk_phone_number}\n")
            f.write(f"Trunk ID: {call_info.trunk_id}\n")
            f.write(f"Call Status: {call_info.call_status}\n")
            f.write(f"Start Time: {call_info.start_time}\n")
            f.write(f"End Time: {call_info.end_time}\n")
            f.write(f"Duration: {call_info.duration_seconds:.2f} seconds\n")
            
            if call_info.twilio_call_sid:
                f.write(f"Twilio Call SID: {call_info.twilio_call_sid}\n")
            if call_info.twilio_account_sid:
                f.write(f"Twilio Account SID: {call_info.twilio_account_sid}\n")
            if call_info.language_detected:
                f.write(f"Language Detected: {call_info.language_detected}\n")
            
            f.write("\n" + "=" * 80 + "\n")
            f.write("CONVERSATION TRANSCRIPT\n")
            f.write("=" * 80 + "\n\n")
            
            # Merge and sort transcriptions and responses by timestamp
            all_messages = []
            all_messages.extend(call_info.transcriptions)
            all_messages.extend(call_info.agent_responses)
            all_messages.sort(key=lambda x: x['timestamp'])
            
            for msg in all_messages:
                speaker = "👤 USER" if msg['speaker'] == 'USER' else "🤖 AGENT"

                # Add source and type information for better tracking
                source_info = ""
                if msg.get('source'):
                    source_info = f" [{msg['source']}]"

                # Add type-specific information
                if msg.get('type') == 'livekit_transcription':
                    source_info += " [LIVE_TRANSCRIPTION]"
                    if msg.get('track_id'):
                        source_info += f" [Track: {msg['track_id'][:8]}...]"
                elif msg.get('type') == 'chat_message':
                    source_info += " [CHAT]"
                elif msg.get('type') == 'transcription':
                    source_info += " [STT]"
                elif msg.get('type') == 'response':
                    source_info += " [RESPONSE]"

                f.write(f"[{msg['timestamp']}] {speaker}{source_info}:\n")
                f.write(f"{msg['text']}\n\n")
            
            f.write("=" * 80 + "\n")
            f.write("RAW JSON DATA\n")
            f.write("=" * 80 + "\n")
            f.write(json.dumps(call_data, indent=2, ensure_ascii=False))
    
    def get_active_calls(self) -> Dict[str, CallInfo]:
        """Get currently active calls"""
        with self.lock:
            return self.active_calls.copy()

# Global call logger instance
call_logger = CallLogger()
