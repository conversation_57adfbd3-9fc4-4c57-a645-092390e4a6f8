#!/usr/bin/env python3
"""
Simple test script to verify transcription logging functionality.
This script tests only the logging components without loading heavy dependencies.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.logging.call_logger import call_logger
from livekit import rtc

class MockParticipant:
    """Mock participant for testing"""
    def __init__(self, identity: str, kind: rtc.ParticipantKind):
        self.identity = identity
        self.kind = kind
        self.attributes = {
            'sip.phoneNumber': '+1234567890',
            'sip.trunkPhoneNumber': '+0987654321',
            'sip.trunkID': 'test-trunk-001',
            'sip.callStatus': 'in-progress',
            'sip.callID': f'test-call-{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        }

async def test_transcription_logging_directly():
    """Test the transcription logging functionality directly"""
    print("🧪 Testing transcription logging functions...")
    
    # Create mock participant (SIP user)
    sip_participant = MockParticipant("user_12345", rtc.ParticipantKind.PARTICIPANT_KIND_SIP)
    
    # Start a test call
    call_id = call_logger.start_call(sip_participant)
    if not call_id:
        print("❌ Failed to start test call")
        return False
    
    print(f"✅ Test call started with ID: {call_id}")
    
    try:
        # Test 1: Log user transcription stream
        print("\n📝 Testing user transcription stream logging...")
        call_logger.log_transcription_stream(
            call_id,
            "Hello, I need help with my account",
            "USER",
            track_id="track_12345",
            participant_identity="user_12345"
        )
        
        # Test 2: Log agent transcription stream
        print("📝 Testing agent transcription stream logging...")
        call_logger.log_transcription_stream(
            call_id,
            "I'd be happy to help you with your account. What specific issue are you experiencing?",
            "AGENT",
            track_id="track_67890",
            participant_identity="agent_voice_assistant"
        )
        
        # Test 3: Log chat message
        print("📝 Testing chat message logging...")
        call_logger.log_chat_message(
            call_id,
            "I can't access my online banking",
            "user_12345"
        )
        
        # Test 4: Log another agent transcription
        print("📝 Testing another agent transcription...")
        call_logger.log_transcription_stream(
            call_id,
            "Let me help you troubleshoot your online banking access. Can you tell me what error message you're seeing?",
            "AGENT",
            track_id="track_67891",
            participant_identity="agent_voice_assistant"
        )
        
        # Test 5: Log traditional user transcription (for comparison)
        print("📝 Testing traditional user transcription...")
        call_logger.log_user_transcription(
            call_id,
            "The error says 'Invalid credentials'"
        )
        
        # Test 6: Log traditional agent response (for comparison)
        print("📝 Testing traditional agent response...")
        call_logger.log_agent_response(
            call_id,
            "I see the issue. Let me help you reset your password."
        )
        
        # Set language detection
        call_logger.set_language(call_id, "English")
        
        print("\n✅ All transcription logging tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during transcription testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # End the test call
        call_logger.end_call(call_id)
        print(f"📞 Test call ended: {call_id}")

def check_log_files():
    """Check if log files were created properly"""
    print("\n📁 Checking log files...")
    
    log_dir = "call_logs"
    if not os.path.exists(log_dir):
        print(f"❌ Log directory {log_dir} does not exist")
        return False
    
    # List recent log files
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.txt')]
    if log_files:
        print(f"✅ Found {len(log_files)} log files:")
        for log_file in sorted(log_files)[-5:]:  # Show last 5 files
            print(f"  📄 {log_file}")
        
        # Show content of the most recent call log
        call_logs = [f for f in log_files if f.startswith('call_') and not f.startswith('call_events_')]
        if call_logs:
            latest_call_log = sorted(call_logs)[-1]
            print(f"\n📖 Content of latest call log ({latest_call_log}):")
            try:
                with open(os.path.join(log_dir, latest_call_log), 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("=" * 60)
                    print(content)
                    print("=" * 60)
            except Exception as e:
                print(f"❌ Error reading log file: {e}")
    else:
        print("❌ No log files found")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 Starting Simple Transcription Logging Tests")
    print("=" * 60)
    
    success = True
    
    # Test 1: Direct transcription logging
    if not await test_transcription_logging_directly():
        success = False
    
    # Test 2: Check log files
    if not check_log_files():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! Transcription logging is working correctly.")
        print("\n📋 What was tested:")
        print("- ✅ LiveKit transcription stream logging")
        print("- ✅ Chat message logging")
        print("- ✅ Traditional STT transcription logging")
        print("- ✅ Enhanced log file format with source tracking")
        print("\n🔍 Log file features verified:")
        print("- [LiveKit_TextStream] [LIVE_TRANSCRIPTION] tags")
        print("- [LiveKit_Chat] [CHAT] tags")
        print("- Track ID correlation")
        print("- Participant identity tracking")
        print("- Mixed transcription sources in same log")
        print("\n📋 Next steps:")
        print("1. Run your voice agent: python langgraph-agent.py start")
        print("2. Make a test call to your Twilio number")
        print("3. Speak during the call and check call_logs/ for real-time transcriptions")
        print("4. Look for [LIVE_TRANSCRIPTION] tags in the logs")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
