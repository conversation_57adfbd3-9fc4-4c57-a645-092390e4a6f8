import React, { useState, useEffect } from 'react';
import { Mic, MessageSquare, Volume2, Settings, Phone, Activity } from 'lucide-react';

interface ModelConfig {
  [key: string]: {
    base_url: string;
    language: string;
  };
}

const models = {
  stt: {
    'whisper-large-v3-turbo': {
      base_url: 'https://api.groq.com/openai/v1/audio/translations',
      language: 'en'
    },
    'whisper-large-v3': {
      base_url: 'https://api.groq.com/openai/v1/audio/translations',
      language: 'en'
    }
  },
  llm: {
    'llama/llama-4-maverick-17b-128e-instruct': {
      base_url: 'https://api.groq.com/openai/v1/models',
      language: 'en'
    },
    'llama-3.1-8b-instant': {
      base_url: 'https://api.groq.com/openai/v1/models',
      language: 'en'
    },
    'llama3-8b-8192': {
      base_url: 'https://api.groq.com/openai/v1/models',
      language: 'en'
    }
  },
  tts: {
    'simba-multilingual': {
      base_url: 'https://speechify.com/',
      language: 'en'
    }
  }
};

function App() {
  const [selectedSTT, setSelectedSTT] = useState('whisper-large-v3-turbo');
  const [selectedLLM, setSelectedLLM] = useState('llama-3.1-8b-instant');
  const [selectedTTS, setSelectedTTS] = useState('simba-multilingual');
  
  const [apiKeys, setApiKeys] = useState({
    stt: '',
    llm: '',
    tts: ''
  });

  const [audioLevel, setAudioLevel] = useState(0);
  const [transcription, setTranscription] = useState('');
  const [voiceLogs, setVoiceLogs] = useState<string[]>([]);
  const [isRecording, setIsRecording] = useState(false);

  // Simulate audio level updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (isRecording) {
        setAudioLevel(Math.random() * 100);
      } else {
        setAudioLevel(0);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [isRecording]);

  const handleApiKeyChange = (service: keyof typeof apiKeys, value: string) => {
    setApiKeys(prev => ({ ...prev, [service]: value }));
  };

  const startRecording = async () => {
    setIsRecording(true);
    // API call to start recording: POST /api/start-recording
    try {
      const response = await fetch('/api/start-recording', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stt_model: selectedSTT,
          stt_api_key: apiKeys.stt
        })
      });
      // Handle response
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopRecording = async () => {
    setIsRecording(false);
    // API call to stop recording: POST /api/stop-recording
    try {
      const response = await fetch('/api/stop-recording', {
        method: 'POST'
      });
      // Handle response
    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  };

  const processWithLLM = async (text: string) => {
    // API call to process with LLM: POST /api/process-llm
    try {
      const response = await fetch('/api/process-llm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          llm_model: selectedLLM,
          llm_api_key: apiKeys.llm
        })
      });
      const result = await response.json();
      return result.response;
    } catch (error) {
      console.error('Failed to process with LLM:', error);
      return null;
    }
  };

  const synthesizeSpeech = async (text: string) => {
    // API call to synthesize speech: POST /api/synthesize-speech
    try {
      const response = await fetch('/api/synthesize-speech', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          tts_model: selectedTTS,
          tts_api_key: apiKeys.tts
        })
      });
      // Handle audio response
    } catch (error) {
      console.error('Failed to synthesize speech:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">AI Conversation Interface</h1>
          <p className="text-purple-200">Professional Voice AI System Dashboard</p>
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Speech to Text Configuration */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <Mic className="w-6 h-6 text-blue-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Speech to Text</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">STT Model</label>
                <select
                  value={selectedSTT}
                  onChange={(e) => setSelectedSTT(e.target.value)}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                >
                  {Object.keys(models.stt).map((model) => (
                    <option key={model} value={model} className="bg-purple-800 text-white">
                      {model}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">STT API Key</label>
                <input
                  type="password"
                  value={apiKeys.stt}
                  onChange={(e) => handleApiKeyChange('stt', e.target.value)}
                  placeholder="Enter your STT API key"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Language Model Configuration */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <MessageSquare className="w-6 h-6 text-green-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Language Model</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">LLM Model</label>
                <select
                  value={selectedLLM}
                  onChange={(e) => setSelectedLLM(e.target.value)}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                >
                  {Object.keys(models.llm).map((model) => (
                    <option key={model} value={model} className="bg-purple-800 text-white">
                      {model}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">LLM API Key</label>
                <input
                  type="password"
                  value={apiKeys.llm}
                  onChange={(e) => handleApiKeyChange('llm', e.target.value)}
                  placeholder="Enter your LLM API key"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Text to Speech Configuration */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <Volume2 className="w-6 h-6 text-orange-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Text to Speech</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">TTS Model</label>
                <select
                  value={selectedTTS}
                  onChange={(e) => setSelectedTTS(e.target.value)}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                >
                  {Object.keys(models.tts).map((model) => (
                    <option key={model} value={model} className="bg-purple-800 text-white">
                      {model}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">TTS API Key</label>
                <input
                  type="password"
                  value={apiKeys.tts}
                  onChange={(e) => handleApiKeyChange('tts', e.target.value)}
                  placeholder="Enter your TTS API key"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Save Settings Button */}
        <div className="flex justify-center mb-8">
          <button
            onClick={() => {
              // Save all settings - API call to backend
              console.log('Saving settings:', {
                stt: { model: selectedSTT, apiKey: apiKeys.stt },
                llm: { model: selectedLLM, apiKey: apiKeys.llm },
                tts: { model: selectedTTS, apiKey: apiKeys.tts }
              });
              // POST /api/save-settings
            }}
            className="px-8 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
          >
            Save Settings
          </button>
        </div>

        {/* Monitoring Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Audio Input Levels */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <Activity className="w-6 h-6 text-blue-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Audio Input Levels</h2>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <span className="text-purple-200 text-sm font-medium w-16">Level:</span>
                <div className="flex-1 bg-white/10 rounded-full h-4 relative overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 transition-all duration-100 ease-out"
                    style={{ width: `${audioLevel}%` }}
                  />
                </div>
                <span className="text-white text-sm font-semibold w-12">{Math.round(audioLevel)}%</span>
              </div>
              
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-green-400 text-lg font-bold">Normal</div>
                  <div className="text-purple-200 text-sm">0-60%</div>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-yellow-400 text-lg font-bold">High</div>
                  <div className="text-purple-200 text-sm">60-80%</div>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-red-400 text-lg font-bold">Peak</div>
                  <div className="text-purple-200 text-sm">80-100%</div>
                </div>
              </div>
            </div>
          </div>

          {/* Transcription */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center mb-4">
              <Settings className="w-6 h-6 text-purple-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Live Transcription</h2>
            </div>
            
            <div className="bg-white/5 rounded-xl p-4 h-48 overflow-y-auto">
              {transcription ? (
                <p className="text-white leading-relaxed">{transcription}</p>
              ) : (
                <p className="text-purple-300 italic">Transcription will appear here...</p>
              )}
            </div>
            
            <div className="mt-4 flex space-x-2">
              <button 
                onClick={() => setTranscription('')}
                className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors duration-200"
              >
                Clear
              </button>
              <button 
                onClick={() => {
                  // Save transcription - API call to backend
                  console.log('Saving transcription:', transcription);
                  // POST /api/save-transcription
                }}
                disabled={!transcription}
                className="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 disabled:bg-gray-500/20 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors duration-200"
              >
                Save
              </button>
            </div>
          </div>
        </div>

        {/* Voice Call Logs */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <Phone className="w-6 h-6 text-green-300 mr-3" />
              <h2 className="text-xl font-semibold text-white">Voice Call Logs</h2>
            </div>
            <button className="px-4 py-2 bg-green-500/20 text-green-300 rounded-lg hover:bg-green-500/30 transition-colors duration-200">
              Export Logs
            </button>
          </div>
          
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {voiceLogs.length > 0 ? (
              voiceLogs.map((log, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-4 border-l-4 border-blue-400">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-white">{log}</p>
                      <p className="text-purple-300 text-sm mt-1">
                        {new Date().toLocaleString()}
                      </p>
                    </div>
                    <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs">
                      Completed
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <Phone className="w-12 h-12 text-purple-400 mx-auto mb-4 opacity-50" />
                <p className="text-purple-300 italic">No voice call logs yet</p>
                <p className="text-purple-400 text-sm mt-2">Start a conversation to see logs here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;