[ 2025-07-31 17:22:27,853 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-31 17:22:28,464 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.6118 seconds.
[ 2025-07-31 17:22:37,428 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-31 17:22:47,254 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:22:47,256 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:22:47,257 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-31 17:25:10,027 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:25:10,028 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:25:10,029 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-31 17:25:10,029 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-31 17:25:10,033 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-31 17:25:10,083 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,083 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,084 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,084 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,084 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,085 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,085 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,086 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,087 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,089 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,090 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,090 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:10,109 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,111 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,111 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,111 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,112 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,112 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,113 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,113 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,113 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,114 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,114 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,114 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:10,624 ] 784 livekit.agents - INFO - registered worker
[ 2025-07-31 17:25:45,541 ] 855 livekit.agents - INFO - received job request
[ 2025-07-31 17:25:45,733 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:25:45,736 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:25:47,988 ] 576 root - INFO - Call logging started for call ID: SCL_9CAxigDtHaqU
[ 2025-07-31 17:25:48,612 ] 632 root - ERROR - Voice agent error: Cannot register an async callback with `.on()`. Use `asyncio.create_task` within your synchronous callback instead.
[ 2025-07-31 17:25:48,613 ] 639 root - INFO - Call logging ended for call ID: SCL_9CAxigDtHaqU
[ 2025-07-31 17:25:48,614 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 587, in entrypoint
    session = CallLoggingSession(
              ^^^^^^^^^^^^^^^^^^^
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 528, in __init__
    super().__init__(*args, **kwargs)
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 77, in __init__
    self._setup_transcription_capture()
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 83, in _setup_transcription_capture
    @self.on("user_input_transcribed")
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\rtc\event_emitter.py", line 172, in decorator
    self.on(event, callback)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\rtc\event_emitter.py", line 161, in on
    raise ValueError(
ValueError: Cannot register an async callback with `.on()`. Use `asyncio.create_task` within your synchronous callback instead.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 633, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\twillio-integrated\dev-calling-agent\langgraph-agent.py] line number [587] error message [Cannot register an async callback with `.on()`. Use `asyncio.create_task` within your synchronous callback instead.]
[ 2025-07-31 17:26:40,360 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-07-31 17:27:30,814 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-07-31 17:27:33,325 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-07-31 17:35:12,232 ] 477 livekit.agents - INFO - draining worker
[ 2025-07-31 17:35:12,233 ] 560 livekit.agents - INFO - shutting down worker
