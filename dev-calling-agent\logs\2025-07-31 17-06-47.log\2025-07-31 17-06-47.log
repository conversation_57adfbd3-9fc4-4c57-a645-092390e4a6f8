[ 2025-07-31 17:06:52,198 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-07-31 17:06:52,672 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4732 seconds.
[ 2025-07-31 17:06:58,949 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-07-31 17:07:08,409 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:07:08,409 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:07:08,411 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-07-31 17:08:19,224 ] 101 root - INFO - Fast RAG chain ready
[ 2025-07-31 17:08:19,225 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-07-31 17:08:19,226 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-07-31 17:08:19,226 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-07-31 17:08:19,238 ] 374 livekit.agents - INFO - starting worker
[ 2025-07-31 17:08:19,283 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,283 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,286 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,288 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,290 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,291 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,292 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,292 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,293 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,293 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,293 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,294 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:19,324 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,325 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,326 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,328 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,328 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,328 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,329 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,329 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,329 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,329 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,330 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,330 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:08:19,861 ] 784 livekit.agents - INFO - registered worker
[ 2025-07-31 17:08:58,637 ] 855 livekit.agents - INFO - received job request
[ 2025-07-31 17:08:58,918 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-07-31 17:08:58,920 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-07-31 17:09:00,338 ] 500 root - INFO - Call logging started for call ID: SCL_ar4hkMzEGHwZ
[ 2025-07-31 17:09:00,995 ] 553 root - ERROR - Voice agent error: AgentSession.generate_reply() takes 1 positional argument but 2 were given
[ 2025-07-31 17:09:00,997 ] 560 root - INFO - Call logging ended for call ID: SCL_ar4hkMzEGHwZ
[ 2025-07-31 17:09:00,997 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 545, in entrypoint
    await session.generate_reply(
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 468, in generate_reply
    reply = await super().generate_reply(instructions, **kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: AgentSession.generate_reply() takes 1 positional argument but 2 were given

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\twillio-integrated\dev-calling-agent\langgraph-agent.py", line 554, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\twillio-integrated\dev-calling-agent\langgraph-agent.py] line number [545] error message [AgentSession.generate_reply() takes 1 positional argument but 2 were given]
[ 2025-07-31 17:09:37,628 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-07-31 17:09:37,628 ] 207 root - INFO - \u26a1 Language detection: 1.312s
[ 2025-07-31 17:09:37,628 ] 337 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-07-31 17:09:37,629 ] 341 root - INFO - Enhanced web search query: Who is the President of India as of 2025 as of 2025
[ 2025-07-31 17:09:37,630 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-31 17:09:37,631 ] 45 root - INFO - \U0001f310 Fast web search: 'Who is the President of India ...'
[ 2025-07-31 17:09:40,135 ] 59 root - INFO - \u26a1 Web search: 2.50s, 2 results
[ 2025-07-31 17:09:40,137 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5064 seconds.
[ 2025-07-31 17:09:40,137 ] 353 root - INFO - \U0001f310 Web search: 3.82s, 2 results
[ 2025-07-31 17:09:56,982 ] 341 root - INFO - Enhanced web search query: What is the weather in Kolkata as of 2025 as of 2025
[ 2025-07-31 17:09:56,983 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-07-31 17:09:56,985 ] 45 root - INFO - \U0001f310 Fast web search: 'What is the weather in Kolkata...'
[ 2025-07-31 17:10:00,336 ] 59 root - INFO - \u26a1 Web search: 3.35s, 2 results
[ 2025-07-31 17:10:00,337 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.3537 seconds.
[ 2025-07-31 17:10:00,338 ] 353 root - INFO - \U0001f310 Web search: 3.36s, 2 results
[ 2025-07-31 17:10:21,617 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-07-31 17:10:21,632 ] 465 livekit.agents - WARNING - rotate_segment called while previous segment is still being rotated
[ 2025-07-31 17:10:44,682 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-07-31 17:10:50,189 ] 477 livekit.agents - INFO - draining worker
[ 2025-07-31 17:10:50,190 ] 560 livekit.agents - INFO - shutting down worker
