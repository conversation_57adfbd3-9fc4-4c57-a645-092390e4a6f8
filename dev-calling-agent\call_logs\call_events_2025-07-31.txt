{"timestamp": "2025-07-31T17:25:47.986845", "call_id": "SCL_9CAxigDtHaqU", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-07-31T17:25:48.612047", "call_id": "SCL_9CAxigDtHaqU", "event_type": "CALL_ENDED", "data": {"duration": 0.625202, "end_time": "2025-07-31T17:25:48.612047"}}
{"timestamp": "2025-07-31T17:33:50.958758", "call_id": "test-call-20250731_173350", "event_type": "CALL_STARTED", "data": {"caller": "+**********", "trunk": "+**********", "status": "in-progress"}}
{"timestamp": "2025-07-31T17:33:50.959941", "call_id": "test-call-20250731_173350", "event_type": "USER_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.959925", "speaker": "USER", "text": "Hello, I need help with my account", "type": "transcription"}}
{"timestamp": "2025-07-31T17:33:50.960422", "call_id": "test-call-20250731_173350", "event_type": "USER_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.960411", "speaker": "USER", "text": "Hello, I need help with my account", "type": "livekit_transcription", "track_id": "user_stt", "participant_identity": "user_participant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.960982", "call_id": "test-call-20250731_173350", "event_type": "AGENT_RESPONSE", "data": {"timestamp": "2025-07-31T17:33:50.960971", "speaker": "AGENT", "text": "I'd be happy to help you with your account. What specific issue are you experiencing?", "type": "response"}}
{"timestamp": "2025-07-31T17:33:50.961385", "call_id": "test-call-20250731_173350", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.961375", "speaker": "AGENT", "text": "I'd be happy to help you with your account. What specific issue are you experiencing?", "type": "livekit_transcription", "track_id": "agent_response", "participant_identity": "agent_session", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.961919", "call_id": "test-call-20250731_173350", "event_type": "USER_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.961908", "speaker": "USER", "text": "I can't access my online banking", "type": "transcription"}}
{"timestamp": "2025-07-31T17:33:50.962306", "call_id": "test-call-20250731_173350", "event_type": "USER_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.962297", "speaker": "USER", "text": "I can't access my online banking", "type": "livekit_transcription", "track_id": "user_turn_1", "participant_identity": "user_participant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.962831", "call_id": "test-call-20250731_173350", "event_type": "AGENT_RESPONSE", "data": {"timestamp": "2025-07-31T17:33:50.962819", "speaker": "AGENT", "text": "I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.", "type": "response"}}
{"timestamp": "2025-07-31T17:33:50.963152", "call_id": "test-call-20250731_173350", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.963145", "speaker": "AGENT", "text": "I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.", "type": "livekit_transcription", "track_id": "agent_turn_2", "participant_identity": "agent_participant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.963554", "call_id": "test-call-20250731_173350", "event_type": "USER_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.963544", "speaker": "USER", "text": "The error says 'Invalid credentials'", "type": "transcription"}}
{"timestamp": "2025-07-31T17:33:50.963873", "call_id": "test-call-20250731_173350", "event_type": "USER_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.963866", "speaker": "USER", "text": "The error says 'Invalid credentials'", "type": "livekit_transcription", "track_id": "user_turn_3", "participant_identity": "user_participant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.964257", "call_id": "test-call-20250731_173350", "event_type": "AGENT_RESPONSE", "data": {"timestamp": "2025-07-31T17:33:50.964244", "speaker": "AGENT", "text": "I see the issue. Let me help you reset your password securely.", "type": "response"}}
{"timestamp": "2025-07-31T17:33:50.964756", "call_id": "test-call-20250731_173350", "event_type": "AGENT_LIVEKIT_TRANSCRIPTION", "data": {"timestamp": "2025-07-31T17:33:50.964747", "speaker": "AGENT", "text": "I see the issue. Let me help you reset your password securely.", "type": "livekit_transcription", "track_id": "agent_turn_4", "participant_identity": "agent_participant", "source": "LiveKit_TextStream"}}
{"timestamp": "2025-07-31T17:33:50.965149", "call_id": "test-call-20250731_173350", "event_type": "LANGUAGE_DETECTED", "data": {"language": "English (en)"}}
{"timestamp": "2025-07-31T17:33:50.967268", "call_id": "test-call-20250731_173350", "event_type": "CALL_ENDED", "data": {"duration": 0.007701, "end_time": "2025-07-31T17:33:50.966439"}}
{"timestamp": "2025-07-31T17:38:42.962985", "call_id": "SCL_uZpTMMsrbPGK", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-07-31T17:38:43.542986", "call_id": "SCL_uZpTMMsrbPGK", "event_type": "CALL_ENDED", "data": {"duration": 0.578995, "end_time": "2025-07-31T17:38:43.541980"}}
