#!/usr/bin/env python3
"""
Test script to verify enhanced transcription capture functionality.
This script tests the new event-based transcription capture system.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.logging.call_logger import call_logger
from livekit import rtc

class MockParticipant:
    """Mock participant for testing"""
    def __init__(self, identity: str, kind: rtc.ParticipantKind):
        self.identity = identity
        self.kind = kind
        self.attributes = {
            'sip.phoneNumber': '+1234567890',
            'sip.trunkPhoneNumber': '+0987654321',
            'sip.trunkID': 'test-trunk-001',
            'sip.callStatus': 'in-progress',
            'sip.callID': f'test-call-{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        }

class MockMessage:
    """Mock message for testing user input"""
    def __init__(self, content: str):
        self.content = content

async def test_enhanced_transcription_capture():
    """Test the enhanced transcription capture functionality"""
    print("🧪 Testing Enhanced Transcription Capture...")
    
    # Create mock participant (SIP user)
    sip_participant = MockParticipant("user_12345", rtc.ParticipantKind.PARTICIPANT_KIND_SIP)
    
    # Start a test call
    call_id = call_logger.start_call(sip_participant)
    if not call_id:
        print("❌ Failed to start test call")
        return False
    
    print(f"✅ Test call started with ID: {call_id}")
    
    try:
        # Simulate the enhanced transcription capture by directly calling the logging methods
        
        # Test 1: User message (simulating _handle_user_message)
        print("\n📝 Testing user message capture...")
        user_message = "Hello, I need help with my account"
        
        # Traditional logging (existing)
        call_logger.log_user_transcription(call_id, user_message)
        
        # Enhanced stream logging (new)
        call_logger.log_transcription_stream(
            call_id,
            user_message,
            "USER",
            track_id="user_stt",
            participant_identity="user_participant"
        )
        
        # Test 2: Agent response (simulating generate_reply)
        print("📝 Testing agent response capture...")
        agent_response = "I'd be happy to help you with your account. What specific issue are you experiencing?"
        
        # Traditional logging (existing)
        call_logger.log_agent_response(call_id, agent_response)
        
        # Enhanced stream logging (new)
        call_logger.log_transcription_stream(
            call_id,
            agent_response,
            "AGENT",
            track_id="agent_response",
            participant_identity="agent_session"
        )
        
        # Test 3: Multiple conversation turns
        print("📝 Testing conversation flow...")
        
        conversation = [
            ("USER", "I can't access my online banking", "user_stt_2"),
            ("AGENT", "I understand you're having trouble accessing your online banking. Let me help you troubleshoot this issue.", "agent_response_2"),
            ("USER", "The error says 'Invalid credentials'", "user_stt_3"),
            ("AGENT", "I see the issue. Let me help you reset your password securely.", "agent_response_3"),
            ("USER", "That would be great, thank you", "user_stt_4"),
            ("AGENT", "You're welcome! I'll guide you through the password reset process step by step.", "agent_response_4")
        ]
        
        for speaker, text, track_id in conversation:
            if speaker == "USER":
                call_logger.log_user_transcription(call_id, text)
            else:
                call_logger.log_agent_response(call_id, text)
            
            # Enhanced logging for both
            call_logger.log_transcription_stream(
                call_id,
                text,
                speaker,
                track_id=track_id,
                participant_identity=f"{speaker.lower()}_participant"
            )
        
        # Test 4: LiveKit text stream simulation
        print("📝 Testing LiveKit text stream simulation...")
        call_logger.log_transcription_stream(
            call_id,
            "This is a LiveKit transcription stream test",
            "USER",
            track_id="livekit_track_123",
            participant_identity="livekit_user"
        )
        
        # Test 5: Chat message simulation
        print("📝 Testing chat message simulation...")
        call_logger.log_chat_message(
            call_id,
            "This is a chat message test",
            "chat_user_456"
        )
        
        # Set language detection
        call_logger.set_language(call_id, "English (en)")
        
        print("\n✅ All enhanced transcription tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during enhanced transcription testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # End the test call
        call_logger.end_call(call_id)
        print(f"📞 Test call ended: {call_id}")

def check_enhanced_log_files():
    """Check if enhanced log files were created properly"""
    print("\n📁 Checking enhanced log files...")
    
    log_dir = "call_logs"
    if not os.path.exists(log_dir):
        print(f"❌ Log directory {log_dir} does not exist")
        return False
    
    # List recent log files
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.txt')]
    if log_files:
        print(f"✅ Found {len(log_files)} log files:")
        for log_file in sorted(log_files)[-5:]:  # Show last 5 files
            print(f"  📄 {log_file}")
        
        # Show content of the most recent call log
        call_logs = [f for f in log_files if f.startswith('call_') and not f.startswith('call_events_')]
        if call_logs:
            latest_call_log = sorted(call_logs)[-1]
            print(f"\n📖 Content of latest enhanced call log ({latest_call_log}):")
            try:
                with open(os.path.join(log_dir, latest_call_log), 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("=" * 70)
                    print(content)
                    print("=" * 70)
                    
                    # Check for enhanced features
                    enhanced_features = [
                        "[LiveKit_TextStream]",
                        "[LIVE_TRANSCRIPTION]",
                        "[STT]",
                        "[RESPONSE]",
                        "[CHAT]",
                        "Track:",
                        "livekit_transcription",
                        "track_id"
                    ]
                    
                    found_features = []
                    for feature in enhanced_features:
                        if feature in content:
                            found_features.append(feature)
                    
                    print(f"\n🔍 Enhanced features found: {len(found_features)}/{len(enhanced_features)}")
                    for feature in found_features:
                        print(f"  ✅ {feature}")
                    
                    missing_features = [f for f in enhanced_features if f not in found_features]
                    if missing_features:
                        print(f"\n⚠️ Missing features:")
                        for feature in missing_features:
                            print(f"  ❌ {feature}")
                    
            except Exception as e:
                print(f"❌ Error reading log file: {e}")
    else:
        print("❌ No log files found")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 Starting Enhanced Transcription Capture Tests")
    print("=" * 70)
    
    success = True
    
    # Test 1: Enhanced transcription capture
    if not await test_enhanced_transcription_capture():
        success = False
    
    # Test 2: Check enhanced log files
    if not check_enhanced_log_files():
        success = False
    
    print("\n" + "=" * 70)
    if success:
        print("✅ All enhanced tests passed! Transcription capture is working correctly.")
        print("\n📋 Enhanced features verified:")
        print("- ✅ Dual logging (traditional + stream)")
        print("- ✅ Event-based transcription capture")
        print("- ✅ Enhanced log format with source tracking")
        print("- ✅ Track ID correlation")
        print("- ✅ Participant identity tracking")
        print("- ✅ Multiple transcription sources")
        print("\n📋 Next steps:")
        print("1. Run your voice agent: python langgraph-agent.py start")
        print("2. Make a test call to your Twilio number")
        print("3. Speak during the call")
        print("4. Check call_logs/ for enhanced transcription capture")
        print("5. Look for multiple transcription entries per message")
        print("\n🔍 What to expect in real calls:")
        print("- User speech will be logged twice (traditional + enhanced)")
        print("- Agent responses will be logged twice (traditional + enhanced)")
        print("- Enhanced logs will have [LIVE_TRANSCRIPTION] tags")
        print("- Track IDs will correlate transcriptions to audio")
    else:
        print("❌ Some enhanced tests failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
